using Klee.Domain.Entities.Common.Exceptions.VoyageManagement;
using Klee.Domain.Entities.QualificationManagement.Qualifications.Data;
using Klee.Domain.Messages.Commands.VoyageManagement.Voyages;
using Klee.Domain.Messages.Commands.InvoiceManagement.Invoices;
using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles;
using Klee.Domain.Messages.Queries.VoyagePlanning.Data;
using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles.Data;
using Klee.Domain.Services;

namespace Klee.Web.App.Components.Pages.VoyagePlanning;

public class VoyageOrderConfirmationViewModel
{
    #region FIELDS
    private readonly ISrpProcessors _srpProcessors;
    #endregion

    #region PROPERTIES
    // Voyage data
    public string SelectedVesselId { get; set; } = "";
    public DateTime VoyageStartDateTime { get; set; }
    public DateTime VoyageEndDateTime { get; set; }
    public IEnumerable<QualificationTypeIds> RequiredQualifications { get; set; } = new List<QualificationTypeIds>();
    public AvailableCaptainListItem SelectedCaptain { get; set; } = null;
    
    // Vessel information
    public OrganizationVehicleListItem VesselInfo { get; set; } = null;
    
    // Cost calculations
    public double VoyageDurationHours => (VoyageEndDateTime - VoyageStartDateTime).TotalHours;
    public double OperatorCost => VoyageDurationHours * SelectedCaptain.HourlyRateInEuros;
    public double CommissionRate => 0.05; // 5%
    public double CommissionAmount => OperatorCost * CommissionRate;
    public double TotalCost => OperatorCost + CommissionAmount;
    
    // UI state
    public bool IsCreatingVoyage { get; set; } = false;

    // Concurrency handling
    public bool HasConcurrencyConflict { get; set; } = false;
    public string ConcurrencyErrorMessage { get; set; } = "";
    #endregion

    #region CONSTRUCTORS
    public VoyageOrderConfirmationViewModel(ISrpProcessors srpProcessors)
    {
        _srpProcessors = srpProcessors;
    }
    #endregion

    #region METHODS - DATA MANAGEMENT
    public void SetVoyageData(string vesselId, DateTime startDateTime, DateTime endDateTime, 
        IEnumerable<QualificationTypeIds> qualifications, AvailableCaptainListItem selectedCaptain)
    {
        SelectedVesselId = vesselId;
        VoyageStartDateTime = startDateTime;
        VoyageEndDateTime = endDateTime;
        RequiredQualifications = qualifications;
        SelectedCaptain = selectedCaptain;
    }

    public async Task LoadVesselInfoAsync()
    {
        if (string.IsNullOrEmpty(SelectedVesselId))
            return;

        GetOrganizationVehicleListQuery query = new (await _srpProcessors.GetQueryContextAsync());
        
        IReadOnlyList<OrganizationVehicleListItem>? vessels = await _srpProcessors.QueryProcessor.ExecuteAsync(query);
        VesselInfo = vessels.FirstOrDefault(v => v.VehicleId == SelectedVesselId);
    }

    public async Task<bool> CreateVoyageAsync(string bookingOrganizationId)
    {
        IsCreatingVoyage = true;
        HasConcurrencyConflict = false;
        ConcurrencyErrorMessage = "";

        try
        {
            // Create voyage command
            var createVoyageCommand = new CreateVoyageCommand(bookingOrganizationId, await _srpProcessors.GetCommandContextAsync())
            {
                StartDateTime = VoyageStartDateTime,
                EndDateTime = VoyageEndDateTime,
                Description = $"Voyage for {VesselInfo.VehicleName}",
                RequiredQualifications = RequiredQualifications.ToList(),
                VehicleId = SelectedVesselId,
                OperatorId = SelectedCaptain.OperatorId
            };

            // Execute voyage creation
            await _srpProcessors.CommandProcessor.SendAsync(createVoyageCommand);

            // Create invoice command
            var createInvoiceCommand = new CreateVoyageInvoiceCommand(bookingOrganizationId, await _srpProcessors.GetCommandContextAsync())
            {
                OperatorOrganizationId = SelectedCaptain.OrganizationId,
                VoyageId = createVoyageCommand.Result.VoyageId,
                TotalAmountInEuros = TotalCost
            };

            // Execute invoice creation
            await _srpProcessors.CommandProcessor.SendAsync(createInvoiceCommand);

            return true;
        }
        catch (VoyageConcurrencyException ex)
        {
            HasConcurrencyConflict = true;
            ConcurrencyErrorMessage = ex.Message;
            return false;
        }
        catch
        {
            return false;
        }
        finally
        {
            IsCreatingVoyage = false;
        }
    }

    public string GetVoyageDurationDisplay()
    {
        TimeSpan duration = VoyageEndDateTime - VoyageStartDateTime;
        return duration.TotalDays >= 1 ? $"{duration.Days}d {duration.Hours}h" : $"{duration.TotalHours:F1}h";
    }

    public string GetVoyageDateRangeDisplay()
    {
        if (VoyageStartDateTime.Date == VoyageEndDateTime.Date)
        {
            return $"{VoyageStartDateTime:MMM dd, yyyy} ({VoyageStartDateTime:HH:mm} - {VoyageEndDateTime:HH:mm})";
        }
        return $"{VoyageStartDateTime:MMM dd, yyyy HH:mm} - {VoyageEndDateTime:MMM dd, yyyy HH:mm}";
    }

    public void ClearConcurrencyConflict()
    {
        HasConcurrencyConflict = false;
        ConcurrencyErrorMessage = "";
    }
    #endregion
}
