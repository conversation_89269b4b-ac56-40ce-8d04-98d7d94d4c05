@using AntDesign
@using Klee.Web.App.Components.UI
@using Klee.Domain.Entities.QualificationManagement.Qualifications.Data
@using EnumsNET
@using Klee.Domain.Messages.Queries.OrganizationVehicleManagement.OrganizationVehicles.Data

@inherits PlanVoyageComponentViewBase

<!-- Header -->
<div class="flex items-center justify-between mb-6">
    <h1 class="text-2xl font-bold text-teal-700">Plan a Voyage</h1>
    <Button Type="@ButtonType.Default"
            Class="@TailwindStyleStrings.Button.Outline"
            OnClick="NavigateToDashboard">
        Cancel
    </Button>
</div>

<Card Class="@TailwindStyleStrings.Card.Container">
    <div class="p-6">
        @if (IsLoading)
{
    <div class="text-center py-12">
        <Spin Size="SpinSize.Large" />
        <p class="mt-4 text-gray-600">Loading...</p>
    </div>
}
else
{
    <EditForm Model="@ViewModel" OnValidSubmit="HandleSubmit" OnInvalidSubmit="HandleSubmit">
        <DataAnnotationsValidator />
        
        @if (ValidationErrors.Any())
        {
            <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle h-5 w-5 text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <ul class="list-disc pl-5 space-y-1">
                                @foreach (var error in ValidationErrors)
                                {
                                    <li>@error</li>
                                }
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        }

        <div class="space-y-8">
            <!-- Vessel Selection - Full Width -->
            <div class="space-y-6">
                <div class="flex items-center gap-2 mb-2">
                    <i class="fas fa-ship h-5 w-5 text-teal-700"></i>
                    <h2 class="text-xl font-medium">Select your vessel</h2>
                </div>

                <div class="space-y-2">
                    <Select @bind-Value="@ViewModel.SelectedVesselId"
                            TItemValue="string"
                            TItem="string"
                            Placeholder="Choose a vessel"
                            Class="w-full border border-gray-300 rounded-md bg-white text-gray-700 transition-all duration-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-200"
                            AllowClear="false"
                            Id="vessel">
                        <SelectOptions>
                            @foreach (OrganizationVehicleListItem vessel in ViewModel.OrganizationVessels)
                            {
                                <SelectOption TItemValue="string" TItem="string" Value="@vessel.VehicleId" Label="@($"{vessel.VehicleName} - {vessel.VesselTypeDisplayName}")"/>
                            }
                        </SelectOptions>
                    </Select>
                    <ValidationMessage For="@(() => ViewModel.SelectedVesselId)" />
                </div>
            </div>

            <!-- Date Selection and Qualifications - Two Columns -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Date Selection -->
                <div class="space-y-6">
                    <div class="flex items-center gap-2 mb-2">
                        <i class="fas fa-calendar h-5 w-5 text-teal-700"></i>
                        <h2 class="text-xl font-medium">When is your voyage?</h2>
                    </div>

                    <div class="space-y-4">
                        <FormField Label="From" Id="startDate">
                            <DatePicker @bind-Value="@ViewModel.StartDateTime"
                                        ShowTime="true"
                                        Format="dd/MM/yyyy HH:mm"
                                        Placeholder=@("Select start date and time")
                                        Class="@TailwindStyleStrings.Form.Input"
                                        Id="startDate" />
                            <ValidationMessage For="@(() => ViewModel.StartDateTime)" />
                        </FormField>

                        <FormField Label="To" Id="endDate">
                            <DatePicker @bind-Value="@ViewModel.EndDateTime"
                                        ShowTime="true"
                                        Format="dd/MM/yyyy HH:mm"
                                        Placeholder=@("Select end date and time")
                                        Class="@TailwindStyleStrings.Form.Input"
                                        Id="endDate" />
                            <ValidationMessage For="@(() => ViewModel.EndDateTime)" />
                        </FormField>
                    </div>
                </div>

                <!-- Qualifications -->
                <div class="space-y-6">
                    <div class="flex items-center gap-2 mb-2">
                        <h2 class="text-xl font-medium">Requirements</h2>
                    </div>

                    <FormField Label="Qualifications" Id="qualifications">
                        <Select Mode="SelectMode.Multiple"
                                Placeholder="Select required qualifications"
                                @bind-Values="@ViewModel.SelectedQualifications"
                                TItemValue="QualificationTypeIds"
                                TItem="string"
                                Class="@TailwindStyleStrings.Form.Select"
                                EnableSearch
                                AllowClear>
                            <SelectOptions>
                                @foreach (QualificationTypeIds qualification in Enum.GetValues<QualificationTypeIds>())
                                {
                                    @if (qualification != QualificationTypeIds.None)
                                    {
                                        <SelectOption TItemValue="QualificationTypeIds" TItem="string"
                                                      Value="@qualification"
                                                      Label="@qualification.AsString(EnumFormat.DisplayName)" />
                                    }
                                }
                            </SelectOptions>
                        </Select>
                        <ValidationMessage For="@(() => ViewModel.SelectedQualifications)" />
                    </FormField>
                </div>
            </div>
        </div>

        <!-- Submit Button - Full Width -->
        <div class="pt-3 mt-3">
            <Button Type="@ButtonType.Primary"
                    HtmlType="submit"
                    Class="@TailwindStyleStrings.Button.Primary"
                    Style="width: 100%;"
                    Loading="@IsSubmitting">
                Find a Captain
            </Button>
        </div>
    </EditForm>
}
    </div>
</Card>


